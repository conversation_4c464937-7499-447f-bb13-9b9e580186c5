version: '3.8'

services:
  cortex_on:
    build:
      context: ./cortex_on
      dockerfile: Dockerfile
    volumes:
      - ./cortex_on:/app
    env_file:
      - .env
    restart: always
    network_mode: host

  agentic_browser:
    build:
      context: ./ta-browser
      dockerfile: Dockerfile
    volumes:
      - ./ta-browser:/app
    env_file:
      - .env
    restart: always
    network_mode: host

  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    volumes:
      - ./frontend:/app
      - /app/node_modules
    env_file:
      - .env
    depends_on:
      - cortex_on
      - agentic_browser
    restart: always
    network_mode: host
