{"version": 3, "sources": ["../../react-use-websocket/src/lib/constants.ts", "../../react-use-websocket/src/lib/globals.ts", "../../react-use-websocket/src/lib/socket-io.ts", "../../react-use-websocket/src/lib/heartbeat.ts", "../../react-use-websocket/src/lib/manage-subscribers.ts", "../../react-use-websocket/src/lib/util.ts", "../../react-use-websocket/src/lib/attach-listener.ts", "../../react-use-websocket/src/lib/attach-shared-listeners.ts", "../../react-use-websocket/src/lib/create-or-join.ts", "../../react-use-websocket/src/lib/get-url.ts", "../../react-use-websocket/src/lib/proxy.ts", "../../react-use-websocket/src/lib/use-websocket.ts", "../../react-use-websocket/src/lib/use-socket-io.ts", "../../react-use-websocket/src/lib/use-event-source.ts", "../../react-use-websocket/src/index.ts"], "sourcesContent": ["import { EventSourceEventHandlers, EventSourceOptions } from \"./types\";\n\nconst MILLISECONDS = 1;\nconst SECONDS = 1000 * MILLISECONDS;\n\nexport const DEFAULT_OPTIONS = {};\nexport const EMPTY_EVENT_HANDLERS: EventSourceEventHandlers = {};\nexport const DEFAULT_EVENT_SOURCE_OPTIONS: EventSourceOptions = {\n  withCredentials: false,\n  events: EMPTY_EVENT_HANDLERS,\n};\nexport const SOCKET_IO_PING_INTERVAL = 25 * SECONDS;\nexport const SOCKET_IO_PATH = '/socket.io/?EIO=3&transport=websocket';\nexport const SOCKET_IO_PING_CODE = '2';\nexport const DEFAULT_RECONNECT_LIMIT = 20;\nexport const DEFAULT_RECONNECT_INTERVAL_MS = 5000;\nexport const UNPARSABLE_JSON_OBJECT = {};\nexport const DEFAULT_HEARTBEAT = {\n  message: 'ping',\n  timeout: 60000,\n  interval: 25000,\n};\n\nexport enum ReadyState {\n  UNINSTANTIATED = -1,\n  CONNECTING = 0,\n  OPEN = 1,\n  CLOSING = 2,\n  CLOSED = 3,\n}\n\nconst eventSourceSupported = () => {\n  try {\n    return 'EventSource' in globalThis;\n  } catch (e) {\n    return false;\n  }\n}\n\nexport const isReactNative = typeof navigator !== 'undefined' && navigator.product === 'ReactNative';\nexport const isEventSourceSupported = !isReactNative && eventSourceSupported();\n", "import { WebSocketLike } from \"./types\";\n\nexport interface SharedWebSockets {\n  [url: string]: WebSocketLike;\n}\n\nexport const sharedWebSockets: SharedWebSockets = {};\n\nexport const resetWebSockets = (url?: string): void => {\n  if (url && sharedWebSockets.hasOwnProperty(url)) {\n    delete sharedWebSockets[url];\n  } else {\n    for (let url in sharedWebSockets){\n      if (sharedWebSockets.hasOwnProperty(url)){\n        delete sharedWebSockets[url];\n      }\n    }\n  }\n}\n", "import { SOCKET_IO_PING_INTERVAL, SOCKET_IO_PATH, SOCKET_IO_PING_CODE } from './constants';\nimport { QueryParams, SendMessage } from './types';\n\nexport const parseSocketIOUrl = (url: string) => {\n  if (url) {\n    const isSecure = /^https|wss/.test(url);\n    const strippedProtocol = url.replace(/^(https?|wss?)(:\\/\\/)?/, '');\n    const removedFinalBackSlack = strippedProtocol.replace(/\\/$/, '');\n    const protocol = isSecure ? 'wss' : 'ws';\n\n    return `${protocol}://${removedFinalBackSlack}${SOCKET_IO_PATH}`;\n  } else if (url === '') {\n    const isSecure = /^https/.test(window.location.protocol);\n    const protocol = isSecure ? 'wss' : 'ws';\n    const port = window.location.port ? `:${window.location.port}` : '';\n\n    return `${protocol}://${window.location.hostname}${port}${SOCKET_IO_PATH}`;\n  }\n\n  return url;\n};\n\nexport const appendQueryParams = (url: string, params: QueryParams = {}): string => {\n  const hasParamsRegex = /\\?([\\w]+=[\\w]+)/;\n  const alreadyHasParams = hasParamsRegex.test(url);\n\n  const stringified = `${Object.entries(params).reduce((next, [key, value]) => {\n    return next + `${key}=${value}&`;\n  }, '').slice(0, -1)}`;\n\n  return `${url}${alreadyHasParams ? '&' : '?'}${stringified}`;\n};\n\nexport const setUpSocketIOPing = (sendMessage: SendMessage, interval = SOCKET_IO_PING_INTERVAL) => {\n  const ping = () => sendMessage(SOCKET_IO_PING_CODE);\n\n  return window.setInterval(ping, interval);\n};\n", "import { MutableRefObject } from \"react\";\nimport { DEFAULT_HEARTBEAT } from \"./constants\";\nimport { HeartbeatOptions } from \"./types\";\n\nfunction getLastMessageTime(lastMessageTime: MutableRefObject<number> | MutableRefObject<number>[]): number {\n  if (Array.isArray(lastMessageTime)) {\n    return lastMessageTime.reduce((p, c) => { return (p.current > c.current) ? p : c; }).current;\n  }\n  return lastMessageTime.current\n}\n\nexport function heartbeat(ws: WebSocket, lastMessageTime: MutableRefObject<number> | MutableRefObject<number>[], options?: HeartbeatOptions): () => void {\n  const {\n    interval = DEFAULT_HEARTBEAT.interval,\n    timeout = DEFAULT_HEARTBEAT.timeout,\n    message = DEFAULT_HEARTBEAT.message,\n  } = options || {};\n\n  // how often check interval between ping messages\n  // minimum is 100ms\n  // maximum is ${interval / 10}ms\n  const intervalCheck = Math.max(100, interval / 10);\n\n  let lastPingSentAt = Date.now();\n\n  const heartbeatInterval = setInterval(() => {\n    const timeNow = Date.now();\n    const lastMessageReceivedAt = getLastMessageTime(lastMessageTime);\n    if (lastMessageReceivedAt + timeout <= timeNow) {\n      console.warn(`Heartbeat timed out, closing connection, last message received ${timeNow - lastMessageReceivedAt}ms ago, last ping sent ${timeNow - lastPingSentAt}ms ago`);\n      ws.close();\n    } else {\n      if (lastMessageReceivedAt + interval <= timeNow && lastPingSentAt + interval <= timeNow) {\n        try {\n          if (typeof message === 'function') {\n            ws.send(message());\n          } else {\n            ws.send(message);\n          }\n          lastPingSentAt = timeNow;\n        } catch (err: unknown) {\n          console.error(`Heartbeat failed, closing connection`, err instanceof Error ? err.message : err);\n          ws.close();\n        }\n\n      }\n    }\n  }, intervalCheck);\n\n  ws.addEventListener(\"close\", () => {\n    clearInterval(heartbeatInterval);\n  });\n\n  return () => { };\n}\n", "import { Subscriber } from './types';\n\nexport type Subscribers = {\n    [url: string]: Set<Subscriber>,\n}\n  \nconst subscribers: Subscribers = {};\nconst EMPTY_LIST: Subscriber[] = [];\n\nexport const getSubscribers = (url: string): Subscriber[] => {\n    if (hasSubscribers(url)) {\n        return Array.from(subscribers[url]);\n    }\n    return EMPTY_LIST;\n};\n\nexport const hasSubscribers = (url: string): boolean => {\n    return subscribers[url]?.size > 0;\n};\n\nexport const addSubscriber = (url: string, subscriber: Subscriber): void => {\n    subscribers[url] = subscribers[url] || new Set<Subscriber>();\n    subscribers[url].add(subscriber);\n};\n\nexport const removeSubscriber = (url: string, subscriber: Subscriber): void => {\n    subscribers[url].delete(subscriber);\n};\n\nexport const resetSubscribers = (url?: string): void => {\n    if (url && subscribers.hasOwnProperty(url)) {\n        delete subscribers[url];\n    } else {\n        for (let url in subscribers){\n            if (subscribers.hasOwnProperty(url)){\n                delete subscribers[url];\n            }\n        }\n    }\n}\n", "import { WebSocketLike } from './types';\nimport { resetWebSockets } from './globals';\nimport { resetSubscribers } from './manage-subscribers';\n\nexport function assertIsWebSocket (\n    webSocketInstance: WebSocketLike,\n    skip?: boolean,\n): asserts webSocketInstance is WebSocket {\n    if (!skip && webSocketInstance instanceof WebSocket === false) throw new Error('');\n};\n\n\nexport function resetGlobalState (url?: string): void {\n    resetSubscribers(url);\n    resetWebSockets(url);\n};\n", "import { MutableRefObject } from 'react';\nimport { setUpSocketIOPing } from './socket-io';\nimport { heartbeat } from './heartbeat';\nimport {\n  DEFAULT_RECONNECT_LIMIT,\n  DEFAULT_RECONNECT_INTERVAL_MS,\n  ReadyState,\n  isEventSourceSupported,\n} from './constants';\nimport { Options, SendMessage, WebSocketLike } from './types';\nimport { assertIsWebSocket } from './util';\n\nexport interface Setters {\n  setLastMessage: (message: WebSocketEventMap['message']) => void;\n  setReadyState: (readyState: ReadyState) => void;\n}\n\nconst bindMessageHandler = (\n  webSocketInstance: WebSocketLike,\n  optionsRef: MutableRefObject<Options>,\n  setLastMessage: Setters['setLastMessage'],\n  lastMessageTime: MutableRefObject<number>,\n) => {\n  webSocketInstance.onmessage = (message: WebSocketEventMap['message']) => {\n    optionsRef.current.onMessage && optionsRef.current.onMessage(message);\n\n    if (typeof lastMessageTime?.current === 'number') {\n      lastMessageTime.current = Date.now();\n    }\n\n    if (typeof optionsRef.current.filter === 'function' && optionsRef.current.filter(message) !== true) {\n      return;\n    }\n    if (\n      optionsRef.current.heartbeat &&\n      typeof optionsRef.current.heartbeat !== \"boolean\" &&\n      optionsRef.current.heartbeat?.returnMessage === message.data\n    ) {\n      return;\n    }\n\n    setLastMessage(message);\n  };\n};\n\nconst bindOpenHandler = (\n  webSocketInstance: WebSocketLike,\n  optionsRef: MutableRefObject<Options>,\n  setReadyState: Setters['setReadyState'],\n  reconnectCount: MutableRefObject<number>,\n  lastMessageTime: MutableRefObject<number>,\n) => {\n  webSocketInstance.onopen = (event: WebSocketEventMap['open']) => {\n    optionsRef.current.onOpen && optionsRef.current.onOpen(event);\n    reconnectCount.current = 0;\n    setReadyState(ReadyState.OPEN);\n    //start heart beat here\n    if (optionsRef.current.heartbeat && webSocketInstance instanceof WebSocket) {\n      const heartbeatOptions =\n        typeof optionsRef.current.heartbeat === \"boolean\"\n          ? undefined\n          : optionsRef.current.heartbeat;\n      lastMessageTime.current = Date.now();\n      heartbeat(webSocketInstance, lastMessageTime, heartbeatOptions);\n    }\n\n  };\n};\n\nconst bindCloseHandler = (\n  webSocketInstance: WebSocketLike,\n  optionsRef: MutableRefObject<Options>,\n  setReadyState: Setters['setReadyState'],\n  reconnect: () => void,\n  reconnectCount: MutableRefObject<number>,\n) => {\n  if (isEventSourceSupported && webSocketInstance instanceof EventSource) {\n    return () => { };\n  }\n  assertIsWebSocket(webSocketInstance, optionsRef.current.skipAssert);\n  let reconnectTimeout: number;\n\n  webSocketInstance.onclose = (event: WebSocketEventMap['close']) => {\n    optionsRef.current.onClose && optionsRef.current.onClose(event);\n    setReadyState(ReadyState.CLOSED);\n    if (optionsRef.current.shouldReconnect && optionsRef.current.shouldReconnect(event)) {\n      const reconnectAttempts = optionsRef.current.reconnectAttempts ?? DEFAULT_RECONNECT_LIMIT;\n      if (reconnectCount.current < reconnectAttempts) {\n        const nextReconnectInterval = typeof optionsRef.current.reconnectInterval === 'function' ?\n          optionsRef.current.reconnectInterval(reconnectCount.current) :\n          optionsRef.current.reconnectInterval;\n\n        reconnectTimeout = window.setTimeout(() => {\n          reconnectCount.current++;\n          reconnect();\n        }, nextReconnectInterval ?? DEFAULT_RECONNECT_INTERVAL_MS);\n      } else {\n        optionsRef.current.onReconnectStop && optionsRef.current.onReconnectStop(reconnectAttempts);\n        console.warn(`Max reconnect attempts of ${reconnectAttempts} exceeded`);\n      }\n    }\n  };\n\n  return () => reconnectTimeout && window.clearTimeout(reconnectTimeout);\n};\n\nconst bindErrorHandler = (\n  webSocketInstance: WebSocketLike,\n  optionsRef: MutableRefObject<Options>,\n  setReadyState: Setters['setReadyState'],\n  reconnect: () => void,\n  reconnectCount: MutableRefObject<number>,\n) => {\n  let reconnectTimeout: number;\n\n  webSocketInstance.onerror = (error: WebSocketEventMap['error']) => {\n    optionsRef.current.onError && optionsRef.current.onError(error);\n    if (isEventSourceSupported && webSocketInstance instanceof EventSource) {\n      optionsRef.current.onClose && optionsRef.current.onClose({\n        ...error,\n        code: 1006,\n        reason: `An error occurred with the EventSource: ${error}`,\n        wasClean: false,\n      });\n\n      setReadyState(ReadyState.CLOSED);\n      webSocketInstance.close();\n    }\n\n    if (optionsRef.current.retryOnError) {\n      if (reconnectCount.current < (optionsRef.current.reconnectAttempts ?? DEFAULT_RECONNECT_LIMIT)) {\n        const nextReconnectInterval = typeof optionsRef.current.reconnectInterval === 'function' ?\n          optionsRef.current.reconnectInterval(reconnectCount.current) :\n          optionsRef.current.reconnectInterval;\n\n        reconnectTimeout = window.setTimeout(() => {\n          reconnectCount.current++;\n          reconnect();\n        }, nextReconnectInterval ?? DEFAULT_RECONNECT_INTERVAL_MS);\n      } else {\n        optionsRef.current.onReconnectStop && optionsRef.current.onReconnectStop(optionsRef.current.reconnectAttempts as number);\n        console.warn(`Max reconnect attempts of ${optionsRef.current.reconnectAttempts} exceeded`);\n      }\n    }\n  };\n\n  return () => reconnectTimeout && window.clearTimeout(reconnectTimeout);\n};\n\nexport const attachListeners = (\n  webSocketInstance: WebSocketLike,\n  setters: Setters,\n  optionsRef: MutableRefObject<Options>,\n  reconnect: () => void,\n  reconnectCount: MutableRefObject<number>,\n  lastMessageTime: MutableRefObject<number>,\n  sendMessage: SendMessage\n): (() => void) => {\n  const { setLastMessage, setReadyState } = setters;\n\n  let interval: number;\n  let cancelReconnectOnClose: () => void;\n  let cancelReconnectOnError: () => void;\n\n  if (optionsRef.current.fromSocketIO) {\n    interval = setUpSocketIOPing(sendMessage);\n  }\n\n  bindMessageHandler(\n    webSocketInstance,\n    optionsRef,\n    setLastMessage,\n    lastMessageTime\n  );\n\n  bindOpenHandler(\n    webSocketInstance,\n    optionsRef,\n    setReadyState,\n    reconnectCount,\n    lastMessageTime,\n  );\n\n  cancelReconnectOnClose = bindCloseHandler(\n    webSocketInstance,\n    optionsRef,\n    setReadyState,\n    reconnect,\n    reconnectCount,\n  );\n\n  cancelReconnectOnError = bindErrorHandler(\n    webSocketInstance,\n    optionsRef,\n    setReadyState,\n    reconnect,\n    reconnectCount,\n  );\n\n  return () => {\n    setReadyState(ReadyState.CLOSING);\n    cancelReconnectOnClose();\n    cancelReconnectOnError();\n    webSocketInstance.close();\n    if (interval) clearInterval(interval);\n  };\n};\n", "import { sharedWebSockets } from './globals';\nimport { DEFAULT_RECONNECT_LIMIT, DEFAULT_RECONNECT_INTERVAL_MS, ReadyState, isEventSourceSupported } from './constants';\nimport { getSubscribers } from './manage-subscribers';\nimport { MutableRefObject } from 'react';\nimport { HeartbeatOptions, Options, SendMessage, WebSocketLike } from './types';\nimport { setUpSocketIOPing } from './socket-io';\nimport { heartbeat } from './heartbeat';\n\nconst bindMessageHandler = (\n  webSocketInstance: WebSocketLike,\n  url: string,\n  heartbeatOptions?: boolean | HeartbeatOptions\n) => {\n  webSocketInstance.onmessage = (message: WebSocketEventMap['message']) => {\n    getSubscribers(url).forEach(subscriber => {\n      if (subscriber.optionsRef.current.onMessage) {\n        subscriber.optionsRef.current.onMessage(message);\n      }\n\n      if (typeof subscriber?.lastMessageTime?.current === 'number') {\n        subscriber.lastMessageTime.current = Date.now();\n      }\n\n      if (\n        typeof subscriber.optionsRef.current.filter === 'function' &&\n        subscriber.optionsRef.current.filter(message) !== true\n      ) {\n        return;\n      }\n\n      if (\n        heartbeatOptions &&\n        typeof heartbeatOptions !== \"boolean\" &&\n        heartbeatOptions?.returnMessage === message.data\n      )\n        return;\n\n      subscriber.setLastMessage(message);\n    });\n  };\n};\n\nconst bindOpenHandler = (\n  webSocketInstance: WebSocketLike,\n  url: string,\n  heartbeatOptions?: boolean | HeartbeatOptions\n) => {\n  webSocketInstance.onopen = (event: WebSocketEventMap['open']) => {\n    const subscribers = getSubscribers(url);\n    subscribers.forEach(subscriber => {\n      subscriber.reconnectCount.current = 0;\n      if (subscriber.optionsRef.current.onOpen) {\n        subscriber.optionsRef.current.onOpen(event);\n      }\n\n      subscriber.setReadyState(ReadyState.OPEN);\n\n      let onMessageCb: () => void;\n\n      if (heartbeatOptions && webSocketInstance instanceof WebSocket) {\n        subscriber.lastMessageTime.current = Date.now();\n      }\n    });\n    if (heartbeatOptions && webSocketInstance instanceof WebSocket) {\n      heartbeat(webSocketInstance, subscribers.map(subscriber => subscriber.lastMessageTime), typeof heartbeatOptions === 'boolean' ? undefined : heartbeatOptions,);\n    }\n  };\n};\n\nconst bindCloseHandler = (\n  webSocketInstance: WebSocketLike,\n  url: string,\n) => {\n  if (webSocketInstance instanceof WebSocket) {\n    webSocketInstance.onclose = (event: WebSocketEventMap['close']) => {\n      getSubscribers(url).forEach(subscriber => {\n        if (subscriber.optionsRef.current.onClose) {\n          subscriber.optionsRef.current.onClose(event);\n        }\n\n        subscriber.setReadyState(ReadyState.CLOSED);\n      });\n\n      delete sharedWebSockets[url];\n\n      getSubscribers(url).forEach(subscriber => {\n        if (\n          subscriber.optionsRef.current.shouldReconnect &&\n          subscriber.optionsRef.current.shouldReconnect(event)\n        ) {\n          const reconnectAttempts = subscriber.optionsRef.current.reconnectAttempts ?? DEFAULT_RECONNECT_LIMIT;\n          if (subscriber.reconnectCount.current < reconnectAttempts) {\n            const nextReconnectInterval = typeof subscriber.optionsRef.current.reconnectInterval === 'function' ?\n              subscriber.optionsRef.current.reconnectInterval(subscriber.reconnectCount.current) :\n              subscriber.optionsRef.current.reconnectInterval;\n\n            setTimeout(() => {\n              subscriber.reconnectCount.current++;\n              subscriber.reconnect.current();\n            }, nextReconnectInterval ?? DEFAULT_RECONNECT_INTERVAL_MS);\n          } else {\n            subscriber.optionsRef.current.onReconnectStop && subscriber.optionsRef.current.onReconnectStop(subscriber.optionsRef.current.reconnectAttempts as number);\n            console.warn(`Max reconnect attempts of ${reconnectAttempts} exceeded`);\n          }\n        }\n      });\n    };\n  }\n};\n\nconst bindErrorHandler = (\n  webSocketInstance: WebSocketLike,\n  url: string,\n) => {\n  webSocketInstance.onerror = (error: WebSocketEventMap['error']) => {\n    getSubscribers(url).forEach(subscriber => {\n      if (subscriber.optionsRef.current.onError) {\n        subscriber.optionsRef.current.onError(error);\n      }\n      if (isEventSourceSupported && webSocketInstance instanceof EventSource) {\n        subscriber.optionsRef.current.onClose && subscriber.optionsRef.current.onClose({\n          ...error,\n          code: 1006,\n          reason: `An error occurred with the EventSource: ${error}`,\n          wasClean: false,\n        });\n\n        subscriber.setReadyState(ReadyState.CLOSED);\n      }\n    });\n    if (isEventSourceSupported && webSocketInstance instanceof EventSource) {\n      webSocketInstance.close();\n    }\n  };\n};\n\nexport const attachSharedListeners = (\n  webSocketInstance: WebSocketLike,\n  url: string,\n  optionsRef: MutableRefObject<Options>,\n  sendMessage: SendMessage,\n) => {\n  let interval: number;\n\n  if (optionsRef.current.fromSocketIO) {\n    interval = setUpSocketIOPing(sendMessage);\n  }\n\n  bindMessageHandler(webSocketInstance, url, optionsRef.current.heartbeat);\n  bindCloseHandler(webSocketInstance, url);\n  bindOpenHandler(webSocketInstance, url, optionsRef.current.heartbeat);\n  bindErrorHandler(webSocketInstance, url);\n\n  return () => {\n    if (interval) clearInterval(interval);\n  };\n};\n", "import { MutableRefObject } from 'react';\nimport { sharedWebSockets } from './globals';\nimport { Options, SendMessage, Subscriber, WebSocketLike } from './types';\nimport { isEventSourceSupported, ReadyState, isReactNative } from './constants';\nimport { attachListeners } from './attach-listener';\nimport { attachSharedListeners } from './attach-shared-listeners';\nimport { addSubscriber, removeSubscriber, hasSubscribers } from './manage-subscribers';\n\n//TODO ensure that all onClose callbacks are called\n\nconst cleanSubscribers = (\n  url: string,\n  subscriber: Subscriber,\n  optionsRef: MutableRefObject<Options>,\n  setReadyState: (readyState: ReadyState) => void,\n  clearSocketIoPingInterval: (() => void) | null,\n) => {\n  return () => {\n    removeSubscriber(url, subscriber);\n    if (!hasSubscribers(url)) {\n      try {\n        const socketLike = sharedWebSockets[url];\n        if (socketLike instanceof WebSocket) {\n          socketLike.onclose = (event: WebSocketEventMap['close']) => {\n            if (optionsRef.current.onClose) {\n              optionsRef.current.onClose(event);\n            }\n            setReadyState(ReadyState.CLOSED);\n          };\n        }\n        socketLike.close();\n      } catch (e) {\n\n      }\n      if (clearSocketIoPingInterval) clearSocketIoPingInterval();\n\n      delete sharedWebSockets[url];\n    }\n  }\n};\n\nexport const createOrJoinSocket = (\n  webSocketRef: MutableRefObject<WebSocketLike | null>,\n  url: string,\n  setReadyState: (readyState: ReadyState) => void,\n  optionsRef: MutableRefObject<Options>,\n  setLastMessage: (message: WebSocketEventMap['message']) => void,\n  startRef: MutableRefObject<() => void>,\n  reconnectCount: MutableRefObject<number>,\n  lastMessageTime: MutableRefObject<number>,\n  sendMessage: SendMessage,\n): (() => void) => {\n  if (!isEventSourceSupported && optionsRef.current.eventSourceOptions) {\n    if (isReactNative) {\n      throw new Error('EventSource is not supported in ReactNative');\n    } else {\n      throw new Error('EventSource is not supported');\n    }\n  }\n\n  if (optionsRef.current.share) {\n    let clearSocketIoPingInterval: ((() => void) | null) = null;\n    if (sharedWebSockets[url] === undefined) {\n      sharedWebSockets[url] = optionsRef.current.eventSourceOptions ?\n        new EventSource(url, optionsRef.current.eventSourceOptions) :\n        new WebSocket(url, optionsRef.current.protocols);\n      webSocketRef.current = sharedWebSockets[url];\n      setReadyState(ReadyState.CONNECTING);\n      clearSocketIoPingInterval = attachSharedListeners(\n        sharedWebSockets[url],\n        url,\n        optionsRef,\n        sendMessage,\n      );\n    } else {\n      webSocketRef.current = sharedWebSockets[url];\n      setReadyState(sharedWebSockets[url].readyState);\n    }\n\n    const subscriber: Subscriber = {\n      setLastMessage,\n      setReadyState,\n      optionsRef,\n      reconnectCount,\n      lastMessageTime,\n      reconnect: startRef,\n    };\n\n    addSubscriber(url, subscriber);\n\n    return cleanSubscribers(\n      url,\n      subscriber,\n      optionsRef,\n      setReadyState,\n      clearSocketIoPingInterval,\n    );\n  } else {\n    webSocketRef.current = optionsRef.current.eventSourceOptions ?\n      new EventSource(url, optionsRef.current.eventSourceOptions) :\n      new WebSocket(url, optionsRef.current.protocols);\n    setReadyState(ReadyState.CONNECTING);\n    if (!webSocketRef.current) {\n      throw new Error('WebSocket failed to be created');\n    }\n\n    return attachListeners(\n      webSocketRef.current,\n      {\n        setLastMessage,\n        setReadyState\n      },\n      optionsRef,\n      startRef.current,\n      reconnectCount,\n      lastMessageTime,\n      sendMessage,\n    );\n  }\n};\n", "import { MutableRefObject } from 'react';\nimport { parseSocketIOUrl, appendQueryParams } from './socket-io';\nimport { Options } from './types';\nimport { DEFAULT_RECONNECT_INTERVAL_MS, DEFAULT_RECONNECT_LIMIT } from './constants';\n\nconst waitFor = (duration: number) => new Promise(resolve => window.setTimeout(resolve, duration));\n\nexport const getUrl = async (\n  url: string | (() => string | Promise<string>),\n  optionsRef: MutableRefObject<Options>,\n  retriedAttempts: number = 0,\n): Promise<string | null> => {\n  let convertedUrl: string;\n\n  if (typeof url === 'function') {\n    try {\n      convertedUrl = await url();\n    } catch (e) {\n      if (\n        optionsRef.current.retryOnError\n      ) {\n        const reconnectLimit = optionsRef.current.reconnectAttempts ?? DEFAULT_RECONNECT_LIMIT;\n        if (retriedAttempts < reconnectLimit) {\n            const nextReconnectInterval = typeof optionsRef.current.reconnectInterval === 'function' ?\n              optionsRef.current.reconnectInterval(retriedAttempts) :\n              optionsRef.current.reconnectInterval;\n    \n            await waitFor(nextReconnectInterval ?? DEFAULT_RECONNECT_INTERVAL_MS);\n            return getUrl(url, optionsRef, retriedAttempts + 1);\n          } else {\n            optionsRef.current.onReconnectStop?.(retriedAttempts);\n            return null;\n          }\n      } else {\n        return null;\n      }\n    }\n  } else {\n    convertedUrl = url;\n  }\n\n  const parsedUrl = optionsRef.current.fromSocketIO ?\n    parseSocketIOUrl(convertedUrl) :\n    convertedUrl;\n\n  const parsedWithQueryParams = optionsRef.current.queryParams ?\n    appendQueryParams(\n      parsedUrl,\n      optionsRef.current.queryParams\n    ) :\n    parsedUrl;\n\n  return parsedWithQueryParams;\n};\n", "import { MutableRefObject } from 'react';\n\ntype IfEquals<X, Y, A=X, B=never> =\n  (<T>() => T extends X ? 1 : 2) extends\n  (<T>() => T extends Y ? 1 : 2) ? A : B;\n\ntype WritableKeys<T> = {\n  [P in keyof T]-?: IfEquals<{ [Q in P]: T[P] }, { -readonly [Q in P]: T[P] }, P>\n}[keyof T];\n\nexport const websocketWrapper = (\n  webSocket: WebSocket,\n  start: MutableRefObject<() => void>,\n): WebSocket => {\n\n  return new Proxy<WebSocket>(webSocket, {\n    get: (obj, key: keyof WebSocket) => {\n      const val = obj[key];\n      if ((key as any) === 'reconnect') return start;\n      if (typeof val === 'function') {\n        console.error('Calling methods directly on the websocket is not supported at this moment. You must use the methods returned by useWebSocket.');\n        \n        //Prevent error thrown by invoking a non-function\n        return () => {};\n      } else {\n        return val;\n      }\n    },\n    set: <T extends WritableKeys<WebSocket>>(obj: WebSocket, key: T, val: WebSocket[T]) => {\n      if (/^on/.test(key)) {\n        console.warn('The websocket\\'s event handlers should be defined through the options object passed into useWebSocket.')\n        return false;\n      } else {\n        obj[key] = val;\n        return true;\n      }\n    },\n  });\n};\n\nexport default websocketWrapper;\n", "import { useEffect, useRef, useState, useCallback, useMemo } from 'react';\nimport { flushSync } from 'react-dom';\nimport { DEFAULT_OPTIONS, isEventSourceSupported, ReadyState, UNPARSABLE_JSON_OBJECT } from './constants';\nimport { createOrJoinSocket } from './create-or-join';\nimport { getUrl } from './get-url';\nimport websocketWrapper from './proxy';\nimport {\n  Options,\n  ReadyStateState,\n  SendMessage,\n  SendJsonMessage,\n  WebSocketMessage,\n  WebSocketHook,\n  WebSocketLike,\n} from './types';\nimport { assertIsWebSocket } from './util';\n\nexport const useWebSocket = <T = unknown>(\n  url: string | (() => string | Promise<string>) | null,\n  options: Options = DEFAULT_OPTIONS,\n  connect: boolean = true,\n): WebSocketHook<T> => {\n  const [lastMessage, setLastMessage] = useState<WebSocketEventMap['message'] | null>(null);\n  const [readyState, setReadyState] = useState<ReadyStateState>({});\n  const lastJsonMessage: T = useMemo(() => {\n    if (!options.disableJson && lastMessage) {\n      try {\n        return JSON.parse(lastMessage.data);\n      } catch (e) {\n        return UNPARSABLE_JSON_OBJECT;\n      }\n    }\n    return null;\n  }, [lastMessage, options.disableJson]);\n  const convertedUrl = useRef<string | null>(null);\n  const webSocketRef = useRef<WebSocketLike | null>(null);\n  const startRef = useRef<() => void>(() => void 0);\n  const reconnectCount = useRef<number>(0);\n  const lastMessageTime = useRef<number>(Date.now());\n  const messageQueue = useRef<WebSocketMessage[]>([]);\n  const webSocketProxy = useRef<WebSocketLike | null>(null);\n  const optionsCache = useRef<Options>(options);\n  optionsCache.current = options;\n\n  const readyStateFromUrl: ReadyState =\n    convertedUrl.current && readyState[convertedUrl.current] !== undefined ?\n      readyState[convertedUrl.current] :\n      url !== null && connect === true ?\n        ReadyState.CONNECTING :\n        ReadyState.UNINSTANTIATED;\n\n  const stringifiedQueryParams = options.queryParams ? JSON.stringify(options.queryParams) : null;\n\n  const sendMessage: SendMessage = useCallback((message, keep = true) => {\n    if (isEventSourceSupported && webSocketRef.current instanceof EventSource) {\n      console.warn('Unable to send a message from an eventSource');\n      return;\n    }\n\n    if (webSocketRef.current?.readyState === ReadyState.OPEN) {\n      assertIsWebSocket(webSocketRef.current, optionsCache.current.skipAssert);\n      webSocketRef.current.send(message);\n    } else if (keep) {\n      messageQueue.current.push(message);\n    }\n  }, []);\n\n  const sendJsonMessage: SendJsonMessage = useCallback((message, keep = true) => {\n    sendMessage(JSON.stringify(message), keep);\n  }, [sendMessage]);\n\n  const getWebSocket = useCallback(() => {\n    if (optionsCache.current.share !== true || (isEventSourceSupported && webSocketRef.current instanceof EventSource)) {\n      return webSocketRef.current;\n    }\n\n    if (webSocketProxy.current === null && webSocketRef.current) {\n      assertIsWebSocket(webSocketRef.current, optionsCache.current.skipAssert);\n      webSocketProxy.current = websocketWrapper(webSocketRef.current, startRef);\n    }\n\n    return webSocketProxy.current;\n  }, []);\n\n  useEffect(() => {\n    if (url !== null && connect === true) {\n      let removeListeners: () => void;\n      let expectClose = false;\n      let createOrJoin = true;\n\n      const start = async () => {\n        convertedUrl.current = await getUrl(url, optionsCache);\n\n        if (convertedUrl.current === null) {\n          console.error('Failed to get a valid URL. WebSocket connection aborted.');\n          convertedUrl.current = 'ABORTED';\n          flushSync(() => setReadyState(prev => ({\n            ...prev,\n            ABORTED: ReadyState.CLOSED,\n          })));\n\n          return;\n        }\n\n        const protectedSetLastMessage = (message: WebSocketEventMap['message']) => {\n          if (!expectClose) {\n            flushSync(() => setLastMessage(message));\n          }\n        };\n\n        const protectedSetReadyState = (state: ReadyState) => {\n          if (!expectClose) {\n            flushSync(() => setReadyState(prev => ({\n              ...prev,\n              ...(convertedUrl.current && { [convertedUrl.current]: state }),\n            })));\n          }\n        };\n\n        if (createOrJoin) {\n          removeListeners = createOrJoinSocket(\n            webSocketRef,\n            convertedUrl.current,\n            protectedSetReadyState,\n            optionsCache,\n            protectedSetLastMessage,\n            startRef,\n            reconnectCount,\n            lastMessageTime,\n            sendMessage,\n          );\n        }\n      };\n\n      startRef.current = () => {\n        if (!expectClose) {\n          if (webSocketProxy.current) webSocketProxy.current = null;\n          removeListeners?.();\n          start();\n        }\n      };\n\n      start();\n      return () => {\n        expectClose = true;\n        createOrJoin = false;\n        if (webSocketProxy.current) webSocketProxy.current = null;\n        removeListeners?.();\n        setLastMessage(null);\n      };\n    } else if (url === null || connect === false) {\n      reconnectCount.current = 0; // reset reconnection attempts\n      setReadyState(prev => ({\n        ...prev,\n        ...(convertedUrl.current && { [convertedUrl.current]: ReadyState.CLOSED }),\n      }));\n    }\n  }, [url, connect, stringifiedQueryParams, sendMessage]);\n\n  useEffect(() => {\n    if (readyStateFromUrl === ReadyState.OPEN) {\n      messageQueue.current.splice(0).forEach(message => {\n        sendMessage(message);\n      });\n    }\n  }, [readyStateFromUrl]);\n\n  return {\n    sendMessage,\n    sendJsonMessage,\n    lastMessage,\n    lastJsonMessage,\n    readyState: readyStateFromUrl,\n    getWebSocket,\n  };\n};\n", "import { useMemo } from 'react'\nimport { useWebSocket } from './use-websocket'\nimport { DEFAULT_OPTIONS } from './constants'\nimport { Options, WebSocketHook } from './types';\n\nexport interface SocketIOMessageData<T = unknown> {\n  type: string,\n  payload: T | null,\n}\n\nconst emptyEvent: SocketIOMessageData<null> = {\n  type: 'empty',\n  payload: null,\n}\n\nconst getSocketData = <T = unknown>(event: WebSocketEventMap['message'] | null): SocketIOMessageData<T | null> => {\n  if (!event || !event.data) {\n    return emptyEvent\n  }\n\n  const match = event.data.match(/\\[.*]/)\n\n  if (!match) {\n    return emptyEvent\n  }\n\n  const data = JSON.parse(match)\n\n  if (!Array.isArray(data) || !data[1]) {\n    return emptyEvent\n  }\n\n  return {\n    type: data[0],\n    payload: data[1],\n  }\n}\n\nexport const useSocketIO = <T = unknown>(\n  url: string | (() => string | Promise<string>) | null,\n  options: Options = DEFAULT_OPTIONS,\n  connect: boolean = true,\n): WebSocketHook<SocketIOMessageData<T | null>, SocketIOMessageData<T | null>> => {\n  const optionsWithSocketIO = useMemo(() => ({\n    ...options,\n    fromSocketIO: true,\n  }), [])\n\n  const {\n    sendMessage,\n    sendJsonMessage,\n    lastMessage,\n    readyState,\n    getWebSocket,\n  } = useWebSocket(\n\n    url,\n    optionsWithSocketIO,\n    connect,\n  );\n\n  const socketIOLastMessage = useMemo(() =>\n    getSocketData<T>(lastMessage), [lastMessage]);\n\n  return {\n    sendMessage,\n    sendJsonMessage,\n    lastMessage: socketIOLastMessage,\n    lastJsonMessage: socketIOLastMessage,\n    readyState,\n    getWebSocket,\n  };\n}\n", "import { useEffect, useRef } from 'react'\nimport { useWebSocket } from './use-websocket'\nimport { DEFAULT_EVENT_SOURCE_OPTIONS, EMPTY_EVENT_HANDLERS } from './constants'\nimport { EventSourceOptions, Options, EventSourceHook, EventSourceEventHandlers } from './types';\n\nexport const useEventSource = (\n  url: string | (() => string | Promise<string>) | null,\n  { withCredentials, events, ...options }: EventSourceOptions = DEFAULT_EVENT_SOURCE_OPTIONS,\n  connect: boolean = true,\n): EventSourceHook => {\n  const optionsWithEventSource: Options = {\n      ...options,\n      eventSourceOptions: {\n        withCredentials,\n      }\n  };\n  const eventsRef = useRef<EventSourceEventHandlers>(EMPTY_EVENT_HANDLERS);\n  if (events) {\n    eventsRef.current = events\n  }\n\n  const {\n    lastMessage,\n    readyState,\n    getWebSocket,\n  } = useWebSocket(\n    url,\n    optionsWithEventSource,\n    connect,\n  );\n\n  useEffect(() => {\n    if (lastMessage?.type) {\n        Object.entries(eventsRef.current).forEach(([type, handler]) => {\n            if (type === lastMessage.type) {\n                handler(lastMessage);\n            }\n        });\n    }\n  }, [lastMessage]);\n\n  return {\n    lastEvent: lastMessage,\n    readyState,\n    getEventSource: getWebSocket,\n  };\n}\n", "export { useWebSocket as default } from './lib/use-websocket';\n\nexport { SendMessage, Options } from './lib/types';\n\nexport { useSocketIO } from './lib/use-socket-io';\n\nexport { ReadyState } from './lib/constants';\n\nexport { useEventSource } from './lib/use-event-source';\n\nexport { resetGlobalState } from './lib/util';\n"], "mappings": ";;;;;;;;;;;;;;;;AAEA,QAAM,eAAe;AACrB,QAAM,UAAU,MAAO;AAEV,YAAA,kBAAkB,CAAA;AAClB,YAAA,uBAAiD,CAAA;AACjD,YAAA,+BAAmD;MAC9D,iBAAiB;MACjB,QAAQ,QAAA;;AAEG,YAAA,0BAA0B,KAAK;AAC/B,YAAA,iBAAiB;AACjB,YAAA,sBAAsB;AACtB,YAAA,0BAA0B;AAC1B,YAAA,gCAAgC;AAChC,YAAA,yBAAyB,CAAA;AACzB,YAAA,oBAAoB;MAC/B,SAAS;MACT,SAAS;MACT,UAAU;;AAGZ,QAAY;AAAZ,KAAA,SAAYA,aAAU;AACpB,MAAAA,YAAAA,YAAA,gBAAA,IAAA,EAAA,IAAA;AACA,MAAAA,YAAAA,YAAA,YAAA,IAAA,CAAA,IAAA;AACA,MAAAA,YAAAA,YAAA,MAAA,IAAA,CAAA,IAAA;AACA,MAAAA,YAAAA,YAAA,SAAA,IAAA,CAAA,IAAA;AACA,MAAAA,YAAAA,YAAA,QAAA,IAAA,CAAA,IAAA;IACF,GANY,eAAU,QAAA,aAAV,aAAU,CAAA,EAAA;AAQtB,QAAM,uBAAuB,WAAA;AAC3B,UAAI;AACF,eAAO,iBAAiB;MAC1B,SAAS,GAAG;AACV,eAAO;MACT;IACF;AAEa,YAAA,gBAAgB,OAAO,cAAc,eAAe,UAAU,YAAY;AAC1E,YAAA,yBAAyB,CAAC,QAAA,iBAAiB,qBAAoB;;;;;;;;;;AClC/D,YAAA,mBAAqC,CAAA;AAE3C,QAAM,kBAAkB,SAAC,KAAY;AAC1C,UAAI,OAAO,QAAA,iBAAiB,eAAe,GAAG,GAAG;AAC/C,eAAO,QAAA,iBAAiB,GAAG;MAC7B,OAAO;AACL,iBAAS,SAAO,QAAA,kBAAiB;AAC/B,cAAI,QAAA,iBAAiB,eAAe,KAAG,GAAE;AACvC,mBAAO,QAAA,iBAAiB,KAAG;UAC7B;QACF;MACF;IACF;AAVa,YAAA,kBAAe;;;;;;;;;;ACR5B,QAAA,cAAA;AAGO,QAAM,mBAAmB,SAAC,KAAW;AAC1C,UAAI,KAAK;AACP,YAAM,WAAW,aAAa,KAAK,GAAG;AACtC,YAAM,mBAAmB,IAAI,QAAQ,0BAA0B,EAAE;AACjE,YAAM,wBAAwB,iBAAiB,QAAQ,OAAO,EAAE;AAChE,YAAM,WAAW,WAAW,QAAQ;AAEpC,eAAO,GAAA,OAAG,UAAQ,KAAA,EAAA,OAAM,qBAAqB,EAAA,OAAG,YAAA,cAAc;MAChE,WAAW,QAAQ,IAAI;AACrB,YAAM,WAAW,SAAS,KAAK,OAAO,SAAS,QAAQ;AACvD,YAAM,WAAW,WAAW,QAAQ;AACpC,YAAM,OAAO,OAAO,SAAS,OAAO,IAAA,OAAI,OAAO,SAAS,IAAI,IAAK;AAEjE,eAAO,GAAA,OAAG,UAAQ,KAAA,EAAA,OAAM,OAAO,SAAS,QAAQ,EAAA,OAAG,IAAI,EAAA,OAAG,YAAA,cAAc;MAC1E;AAEA,aAAO;IACT;AAjBa,YAAA,mBAAgB;AAmBtB,QAAM,oBAAoB,SAAC,KAAa,QAAwB;AAAxB,UAAA,WAAA,QAAA;AAAA,iBAAA,CAAA;MAAwB;AACrE,UAAM,iBAAiB;AACvB,UAAM,mBAAmB,eAAe,KAAK,GAAG;AAEhD,UAAM,cAAc,GAAA,OAAG,OAAO,QAAQ,MAAM,EAAE,OAAO,SAAC,MAAM,IAAY;YAAX,MAAG,GAAA,CAAA,GAAE,QAAK,GAAA,CAAA;AACrE,eAAO,OAAO,GAAA,OAAG,KAAG,GAAA,EAAA,OAAI,OAAK,GAAA;MAC/B,GAAG,EAAE,EAAE,MAAM,GAAG,EAAE,CAAC;AAEnB,aAAO,GAAA,OAAG,GAAG,EAAA,OAAG,mBAAmB,MAAM,GAAG,EAAA,OAAG,WAAW;IAC5D;AATa,YAAA,oBAAiB;AAWvB,QAAM,oBAAoB,SAAC,aAA0B,UAAkC;AAAlC,UAAA,aAAA,QAAA;AAAA,mBAAW,YAAA;MAAuB;AAC5F,UAAM,OAAO,WAAA;AAAM,eAAA,YAAY,YAAA,mBAAmB;MAA/B;AAEnB,aAAO,OAAO,YAAY,MAAM,QAAQ;IAC1C;AAJa,YAAA,oBAAiB;;;;;;;;;ACtB9B,YAAA,YAAA;AAVA,QAAA,cAAA;AAGA,aAAS,mBAAmB,iBAAsE;AAChG,UAAI,MAAM,QAAQ,eAAe,GAAG;AAClC,eAAO,gBAAgB,OAAO,SAAC,GAAG,GAAC;AAAO,iBAAQ,EAAE,UAAU,EAAE,UAAW,IAAI;QAAG,CAAC,EAAE;MACvF;AACA,aAAO,gBAAgB;IACzB;AAEA,aAAgB,UAAU,IAAe,iBAAwE,SAA0B;AACnI,UAAA,KAIF,WAAW,CAAA,GAHb,KAAA,GAAA,UAAA,WAAQ,OAAA,SAAG,YAAA,kBAAkB,WAAQ,IACrC,KAAA,GAAA,SAAA,UAAO,OAAA,SAAG,YAAA,kBAAkB,UAAO,IACnC,KAAA,GAAA,SAAA,UAAO,OAAA,SAAG,YAAA,kBAAkB,UAAO;AAMrC,UAAM,gBAAgB,KAAK,IAAI,KAAK,WAAW,EAAE;AAEjD,UAAI,iBAAiB,KAAK,IAAG;AAE7B,UAAM,oBAAoB,YAAY,WAAA;AACpC,YAAM,UAAU,KAAK,IAAG;AACxB,YAAM,wBAAwB,mBAAmB,eAAe;AAChE,YAAI,wBAAwB,WAAW,SAAS;AAC9C,kBAAQ,KAAK,kEAAA,OAAkE,UAAU,uBAAqB,yBAAA,EAAA,OAA0B,UAAU,gBAAc,QAAA,CAAQ;AACxK,aAAG,MAAK;QACV,OAAO;AACL,cAAI,wBAAwB,YAAY,WAAW,iBAAiB,YAAY,SAAS;AACvF,gBAAI;AACF,kBAAI,OAAO,YAAY,YAAY;AACjC,mBAAG,KAAK,QAAO,CAAE;cACnB,OAAO;AACL,mBAAG,KAAK,OAAO;cACjB;AACA,+BAAiB;YACnB,SAAS,KAAc;AACrB,sBAAQ,MAAM,wCAAwC,eAAe,QAAQ,IAAI,UAAU,GAAG;AAC9F,iBAAG,MAAK;YACV;UAEF;QACF;MACF,GAAG,aAAa;AAEhB,SAAG,iBAAiB,SAAS,WAAA;AAC3B,sBAAc,iBAAiB;MACjC,CAAC;AAED,aAAO,WAAA;MAAQ;IACjB;;;;;;;;;;AChDA,QAAM,cAA2B,CAAA;AACjC,QAAM,aAA2B,CAAA;AAE1B,QAAM,iBAAiB,SAAC,KAAW;AACtC,WAAI,GAAA,QAAA,gBAAe,GAAG,GAAG;AACrB,eAAO,MAAM,KAAK,YAAY,GAAG,CAAC;MACtC;AACA,aAAO;IACX;AALa,YAAA,iBAAc;AAOpB,QAAM,iBAAiB,SAAC,KAAW;;AACtC,eAAO,KAAA,YAAY,GAAG,OAAC,QAAA,OAAA,SAAA,SAAA,GAAE,QAAO;IACpC;AAFa,YAAA,iBAAc;AAIpB,QAAM,gBAAgB,SAAC,KAAa,YAAsB;AAC7D,kBAAY,GAAG,IAAI,YAAY,GAAG,KAAK,oBAAI,IAAG;AAC9C,kBAAY,GAAG,EAAE,IAAI,UAAU;IACnC;AAHa,YAAA,gBAAa;AAKnB,QAAM,mBAAmB,SAAC,KAAa,YAAsB;AAChE,kBAAY,GAAG,EAAE,OAAO,UAAU;IACtC;AAFa,YAAA,mBAAgB;AAItB,QAAM,mBAAmB,SAAC,KAAY;AACzC,UAAI,OAAO,YAAY,eAAe,GAAG,GAAG;AACxC,eAAO,YAAY,GAAG;MAC1B,OAAO;AACH,iBAAS,SAAO,aAAY;AACxB,cAAI,YAAY,eAAe,KAAG,GAAE;AAChC,mBAAO,YAAY,KAAG;UAC1B;QACJ;MACJ;IACJ;AAVa,YAAA,mBAAgB;;;;;;;;;ACzB7B,YAAA,oBAAA;AAQA,YAAA,mBAAA;AAXA,QAAA,YAAA;AACA,QAAA,uBAAA;AAEA,aAAgB,kBACZ,mBACA,MAAc;AAEd,UAAI,CAAC,QAAQ,6BAA6B,cAAc;AAAO,cAAM,IAAI,MAAM,EAAE;IACrF;AAGA,aAAgB,iBAAkB,KAAY;AAC1C,OAAA,GAAA,qBAAA,kBAAiB,GAAG;AACpB,OAAA,GAAA,UAAA,iBAAgB,GAAG;IACvB;;;;;;;;;;;;;;;;;;;;;ACdA,QAAA,cAAA;AACA,QAAA,cAAA;AACA,QAAA,cAAA;AAOA,QAAA,SAAA;AAOA,QAAM,qBAAqB,SACzB,mBACA,YACA,gBACA,iBAAyC;AAEzC,wBAAkB,YAAY,SAAC,SAAqC;;AAClE,mBAAW,QAAQ,aAAa,WAAW,QAAQ,UAAU,OAAO;AAEpE,YAAI,QAAO,oBAAe,QAAf,oBAAe,SAAA,SAAf,gBAAiB,aAAY,UAAU;AAChD,0BAAgB,UAAU,KAAK,IAAG;QACpC;AAEA,YAAI,OAAO,WAAW,QAAQ,WAAW,cAAc,WAAW,QAAQ,OAAO,OAAO,MAAM,MAAM;AAClG;QACF;AACA,YACE,WAAW,QAAQ,aACnB,OAAO,WAAW,QAAQ,cAAc,eACxC,KAAA,WAAW,QAAQ,eAAS,QAAA,OAAA,SAAA,SAAA,GAAE,mBAAkB,QAAQ,MACxD;AACA;QACF;AAEA,uBAAe,OAAO;MACxB;IACF;AAEA,QAAM,kBAAkB,SACtB,mBACA,YACA,eACA,gBACA,iBAAyC;AAEzC,wBAAkB,SAAS,SAAC,OAAgC;AAC1D,mBAAW,QAAQ,UAAU,WAAW,QAAQ,OAAO,KAAK;AAC5D,uBAAe,UAAU;AACzB,sBAAc,YAAA,WAAW,IAAI;AAE7B,YAAI,WAAW,QAAQ,aAAa,6BAA6B,WAAW;AAC1E,cAAM,mBACJ,OAAO,WAAW,QAAQ,cAAc,YACpC,SACA,WAAW,QAAQ;AACzB,0BAAgB,UAAU,KAAK,IAAG;AAClC,WAAA,GAAA,YAAA,WAAU,mBAAmB,iBAAiB,gBAAgB;QAChE;MAEF;IACF;AAEA,QAAM,mBAAmB,SACvB,mBACA,YACA,eACA,WACA,gBAAwC;AAExC,UAAI,YAAA,0BAA0B,6BAA6B,aAAa;AACtE,eAAO,WAAA;QAAQ;MACjB;AACA,OAAA,GAAA,OAAA,mBAAkB,mBAAmB,WAAW,QAAQ,UAAU;AAClE,UAAI;AAEJ,wBAAkB,UAAU,SAAC,OAAiC;;AAC5D,mBAAW,QAAQ,WAAW,WAAW,QAAQ,QAAQ,KAAK;AAC9D,sBAAc,YAAA,WAAW,MAAM;AAC/B,YAAI,WAAW,QAAQ,mBAAmB,WAAW,QAAQ,gBAAgB,KAAK,GAAG;AACnF,cAAM,qBAAoB,KAAA,WAAW,QAAQ,uBAAiB,QAAA,OAAA,SAAA,KAAI,YAAA;AAClE,cAAI,eAAe,UAAU,mBAAmB;AAC9C,gBAAM,wBAAwB,OAAO,WAAW,QAAQ,sBAAsB,aAC5E,WAAW,QAAQ,kBAAkB,eAAe,OAAO,IAC3D,WAAW,QAAQ;AAErB,+BAAmB,OAAO,WAAW,WAAA;AACnC,6BAAe;AACf,wBAAS;YACX,GAAG,0BAAqB,QAArB,0BAAqB,SAArB,wBAAyB,YAAA,6BAA6B;UAC3D,OAAO;AACL,uBAAW,QAAQ,mBAAmB,WAAW,QAAQ,gBAAgB,iBAAiB;AAC1F,oBAAQ,KAAK,6BAAA,OAA6B,mBAAiB,WAAA,CAAW;UACxE;QACF;MACF;AAEA,aAAO,WAAA;AAAM,eAAA,oBAAoB,OAAO,aAAa,gBAAgB;MAAxD;IACf;AAEA,QAAM,mBAAmB,SACvB,mBACA,YACA,eACA,WACA,gBAAwC;AAExC,UAAI;AAEJ,wBAAkB,UAAU,SAAC,OAAiC;;AAC5D,mBAAW,QAAQ,WAAW,WAAW,QAAQ,QAAQ,KAAK;AAC9D,YAAI,YAAA,0BAA0B,6BAA6B,aAAa;AACtE,qBAAW,QAAQ,WAAW,WAAW,QAAQ,QAAO,SAAA,SAAA,CAAA,GACnD,KAAK,GAAA,EACR,MAAM,MACN,QAAQ,2CAAA,OAA2C,KAAK,GACxD,UAAU,MAAK,CAAA,CAAA;AAGjB,wBAAc,YAAA,WAAW,MAAM;AAC/B,4BAAkB,MAAK;QACzB;AAEA,YAAI,WAAW,QAAQ,cAAc;AACnC,cAAI,eAAe,YAAW,KAAA,WAAW,QAAQ,uBAAiB,QAAA,OAAA,SAAA,KAAI,YAAA,0BAA0B;AAC9F,gBAAM,wBAAwB,OAAO,WAAW,QAAQ,sBAAsB,aAC5E,WAAW,QAAQ,kBAAkB,eAAe,OAAO,IAC3D,WAAW,QAAQ;AAErB,+BAAmB,OAAO,WAAW,WAAA;AACnC,6BAAe;AACf,wBAAS;YACX,GAAG,0BAAqB,QAArB,0BAAqB,SAArB,wBAAyB,YAAA,6BAA6B;UAC3D,OAAO;AACL,uBAAW,QAAQ,mBAAmB,WAAW,QAAQ,gBAAgB,WAAW,QAAQ,iBAA2B;AACvH,oBAAQ,KAAK,6BAAA,OAA6B,WAAW,QAAQ,mBAAiB,WAAA,CAAW;UAC3F;QACF;MACF;AAEA,aAAO,WAAA;AAAM,eAAA,oBAAoB,OAAO,aAAa,gBAAgB;MAAxD;IACf;AAEO,QAAM,kBAAkB,SAC7B,mBACA,SACA,YACA,WACA,gBACA,iBACA,aAAwB;AAEhB,UAAA,iBAAkC,QAAO,gBAAzB,gBAAkB,QAAO;AAEjD,UAAI;AACJ,UAAI;AACJ,UAAI;AAEJ,UAAI,WAAW,QAAQ,cAAc;AACnC,oBAAW,GAAA,YAAA,mBAAkB,WAAW;MAC1C;AAEA,yBACE,mBACA,YACA,gBACA,eAAe;AAGjB,sBACE,mBACA,YACA,eACA,gBACA,eAAe;AAGjB,+BAAyB,iBACvB,mBACA,YACA,eACA,WACA,cAAc;AAGhB,+BAAyB,iBACvB,mBACA,YACA,eACA,WACA,cAAc;AAGhB,aAAO,WAAA;AACL,sBAAc,YAAA,WAAW,OAAO;AAChC,+BAAsB;AACtB,+BAAsB;AACtB,0BAAkB,MAAK;AACvB,YAAI;AAAU,wBAAc,QAAQ;MACtC;IACF;AAzDa,YAAA,kBAAe;;;;;;;;;;;;;;;;;;;;;ACrJ5B,QAAA,YAAA;AACA,QAAA,cAAA;AACA,QAAA,uBAAA;AAGA,QAAA,cAAA;AACA,QAAA,cAAA;AAEA,QAAM,qBAAqB,SACzB,mBACA,KACA,kBAA6C;AAE7C,wBAAkB,YAAY,SAAC,SAAqC;AAClE,SAAA,GAAA,qBAAA,gBAAe,GAAG,EAAE,QAAQ,SAAA,YAAU;;AACpC,cAAI,WAAW,WAAW,QAAQ,WAAW;AAC3C,uBAAW,WAAW,QAAQ,UAAU,OAAO;UACjD;AAEA,cAAI,SAAO,KAAA,eAAU,QAAV,eAAU,SAAA,SAAV,WAAY,qBAAe,QAAA,OAAA,SAAA,SAAA,GAAE,aAAY,UAAU;AAC5D,uBAAW,gBAAgB,UAAU,KAAK,IAAG;UAC/C;AAEA,cACE,OAAO,WAAW,WAAW,QAAQ,WAAW,cAChD,WAAW,WAAW,QAAQ,OAAO,OAAO,MAAM,MAClD;AACA;UACF;AAEA,cACE,oBACA,OAAO,qBAAqB,cAC5B,qBAAgB,QAAhB,qBAAgB,SAAA,SAAhB,iBAAkB,mBAAkB,QAAQ;AAE5C;AAEF,qBAAW,eAAe,OAAO;QACnC,CAAC;MACH;IACF;AAEA,QAAM,kBAAkB,SACtB,mBACA,KACA,kBAA6C;AAE7C,wBAAkB,SAAS,SAAC,OAAgC;AAC1D,YAAM,eAAc,GAAA,qBAAA,gBAAe,GAAG;AACtC,oBAAY,QAAQ,SAAA,YAAU;AAC5B,qBAAW,eAAe,UAAU;AACpC,cAAI,WAAW,WAAW,QAAQ,QAAQ;AACxC,uBAAW,WAAW,QAAQ,OAAO,KAAK;UAC5C;AAEA,qBAAW,cAAc,YAAA,WAAW,IAAI;AAExC,cAAI;AAEJ,cAAI,oBAAoB,6BAA6B,WAAW;AAC9D,uBAAW,gBAAgB,UAAU,KAAK,IAAG;UAC/C;QACF,CAAC;AACD,YAAI,oBAAoB,6BAA6B,WAAW;AAC9D,WAAA,GAAA,YAAA,WAAU,mBAAmB,YAAY,IAAI,SAAA,YAAU;AAAI,mBAAA,WAAW;UAAX,CAA0B,GAAG,OAAO,qBAAqB,YAAY,SAAY,gBAAgB;QAC9J;MACF;IACF;AAEA,QAAM,mBAAmB,SACvB,mBACA,KAAW;AAEX,UAAI,6BAA6B,WAAW;AAC1C,0BAAkB,UAAU,SAAC,OAAiC;AAC5D,WAAA,GAAA,qBAAA,gBAAe,GAAG,EAAE,QAAQ,SAAA,YAAU;AACpC,gBAAI,WAAW,WAAW,QAAQ,SAAS;AACzC,yBAAW,WAAW,QAAQ,QAAQ,KAAK;YAC7C;AAEA,uBAAW,cAAc,YAAA,WAAW,MAAM;UAC5C,CAAC;AAED,iBAAO,UAAA,iBAAiB,GAAG;AAE3B,WAAA,GAAA,qBAAA,gBAAe,GAAG,EAAE,QAAQ,SAAA,YAAU;;AACpC,gBACE,WAAW,WAAW,QAAQ,mBAC9B,WAAW,WAAW,QAAQ,gBAAgB,KAAK,GACnD;AACA,kBAAM,qBAAoB,KAAA,WAAW,WAAW,QAAQ,uBAAiB,QAAA,OAAA,SAAA,KAAI,YAAA;AAC7E,kBAAI,WAAW,eAAe,UAAU,mBAAmB;AACzD,oBAAM,wBAAwB,OAAO,WAAW,WAAW,QAAQ,sBAAsB,aACvF,WAAW,WAAW,QAAQ,kBAAkB,WAAW,eAAe,OAAO,IACjF,WAAW,WAAW,QAAQ;AAEhC,2BAAW,WAAA;AACT,6BAAW,eAAe;AAC1B,6BAAW,UAAU,QAAO;gBAC9B,GAAG,0BAAqB,QAArB,0BAAqB,SAArB,wBAAyB,YAAA,6BAA6B;cAC3D,OAAO;AACL,2BAAW,WAAW,QAAQ,mBAAmB,WAAW,WAAW,QAAQ,gBAAgB,WAAW,WAAW,QAAQ,iBAA2B;AACxJ,wBAAQ,KAAK,6BAAA,OAA6B,mBAAiB,WAAA,CAAW;cACxE;YACF;UACF,CAAC;QACH;MACF;IACF;AAEA,QAAM,mBAAmB,SACvB,mBACA,KAAW;AAEX,wBAAkB,UAAU,SAAC,OAAiC;AAC5D,SAAA,GAAA,qBAAA,gBAAe,GAAG,EAAE,QAAQ,SAAA,YAAU;AACpC,cAAI,WAAW,WAAW,QAAQ,SAAS;AACzC,uBAAW,WAAW,QAAQ,QAAQ,KAAK;UAC7C;AACA,cAAI,YAAA,0BAA0B,6BAA6B,aAAa;AACtE,uBAAW,WAAW,QAAQ,WAAW,WAAW,WAAW,QAAQ,QAAO,SAAA,SAAA,CAAA,GACzE,KAAK,GAAA,EACR,MAAM,MACN,QAAQ,2CAAA,OAA2C,KAAK,GACxD,UAAU,MAAK,CAAA,CAAA;AAGjB,uBAAW,cAAc,YAAA,WAAW,MAAM;UAC5C;QACF,CAAC;AACD,YAAI,YAAA,0BAA0B,6BAA6B,aAAa;AACtE,4BAAkB,MAAK;QACzB;MACF;IACF;AAEO,QAAM,wBAAwB,SACnC,mBACA,KACA,YACA,aAAwB;AAExB,UAAI;AAEJ,UAAI,WAAW,QAAQ,cAAc;AACnC,oBAAW,GAAA,YAAA,mBAAkB,WAAW;MAC1C;AAEA,yBAAmB,mBAAmB,KAAK,WAAW,QAAQ,SAAS;AACvE,uBAAiB,mBAAmB,GAAG;AACvC,sBAAgB,mBAAmB,KAAK,WAAW,QAAQ,SAAS;AACpE,uBAAiB,mBAAmB,GAAG;AAEvC,aAAO,WAAA;AACL,YAAI;AAAU,wBAAc,QAAQ;MACtC;IACF;AApBa,YAAA,wBAAqB;;;;;;;;;;ACvIlC,QAAA,YAAA;AAEA,QAAA,cAAA;AACA,QAAA,oBAAA;AACA,QAAA,4BAAA;AACA,QAAA,uBAAA;AAIA,QAAM,mBAAmB,SACvB,KACA,YACA,YACA,eACA,2BAA8C;AAE9C,aAAO,WAAA;AACL,SAAA,GAAA,qBAAA,kBAAiB,KAAK,UAAU;AAChC,YAAI,EAAC,GAAA,qBAAA,gBAAe,GAAG,GAAG;AACxB,cAAI;AACF,gBAAM,aAAa,UAAA,iBAAiB,GAAG;AACvC,gBAAI,sBAAsB,WAAW;AACnC,yBAAW,UAAU,SAAC,OAAiC;AACrD,oBAAI,WAAW,QAAQ,SAAS;AAC9B,6BAAW,QAAQ,QAAQ,KAAK;gBAClC;AACA,8BAAc,YAAA,WAAW,MAAM;cACjC;YACF;AACA,uBAAW,MAAK;UAClB,SAAS,GAAG;UAEZ;AACA,cAAI;AAA2B,sCAAyB;AAExD,iBAAO,UAAA,iBAAiB,GAAG;QAC7B;MACF;IACF;AAEO,QAAM,qBAAqB,SAChC,cACA,KACA,eACA,YACA,gBACA,UACA,gBACA,iBACA,aAAwB;AAExB,UAAI,CAAC,YAAA,0BAA0B,WAAW,QAAQ,oBAAoB;AACpE,YAAI,YAAA,eAAe;AACjB,gBAAM,IAAI,MAAM,6CAA6C;QAC/D,OAAO;AACL,gBAAM,IAAI,MAAM,8BAA8B;QAChD;MACF;AAEA,UAAI,WAAW,QAAQ,OAAO;AAC5B,YAAI,4BAAmD;AACvD,YAAI,UAAA,iBAAiB,GAAG,MAAM,QAAW;AACvC,oBAAA,iBAAiB,GAAG,IAAI,WAAW,QAAQ,qBACzC,IAAI,YAAY,KAAK,WAAW,QAAQ,kBAAkB,IAC1D,IAAI,UAAU,KAAK,WAAW,QAAQ,SAAS;AACjD,uBAAa,UAAU,UAAA,iBAAiB,GAAG;AAC3C,wBAAc,YAAA,WAAW,UAAU;AACnC,uCAA4B,GAAA,0BAAA,uBAC1B,UAAA,iBAAiB,GAAG,GACpB,KACA,YACA,WAAW;QAEf,OAAO;AACL,uBAAa,UAAU,UAAA,iBAAiB,GAAG;AAC3C,wBAAc,UAAA,iBAAiB,GAAG,EAAE,UAAU;QAChD;AAEA,YAAM,aAAyB;UAC7B;UACA;UACA;UACA;UACA;UACA,WAAW;;AAGb,SAAA,GAAA,qBAAA,eAAc,KAAK,UAAU;AAE7B,eAAO,iBACL,KACA,YACA,YACA,eACA,yBAAyB;MAE7B,OAAO;AACL,qBAAa,UAAU,WAAW,QAAQ,qBACxC,IAAI,YAAY,KAAK,WAAW,QAAQ,kBAAkB,IAC1D,IAAI,UAAU,KAAK,WAAW,QAAQ,SAAS;AACjD,sBAAc,YAAA,WAAW,UAAU;AACnC,YAAI,CAAC,aAAa,SAAS;AACzB,gBAAM,IAAI,MAAM,gCAAgC;QAClD;AAEA,gBAAO,GAAA,kBAAA,iBACL,aAAa,SACb;UACE;UACA;WAEF,YACA,SAAS,SACT,gBACA,iBACA,WAAW;MAEf;IACF;AA9Ea,YAAA,qBAAkB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACxC/B,QAAA,cAAA;AAEA,QAAA,cAAA;AAEA,QAAM,UAAU,SAAC,UAAgB;AAAK,aAAA,IAAI,QAAQ,SAAA,SAAO;AAAI,eAAA,OAAO,WAAW,SAAS,QAAQ;MAAnC,CAAoC;IAA3D;AAE/B,QAAM,SAAS,SAAA,OAAA,cAAA;;;;;oGACpB,KACA,YACA,iBAA2B;;;AAA3B,YAAA,oBAAA,QAAA;AAAA,4BAAA;QAA2B;;;;oBAIvB,OAAO,QAAQ,YAAf,QAAA,CAAA,GAAA,EAAA;;;;AAEe,qBAAA,CAAA,GAAM,IAAG,CAAE;;AAA1B,6BAAe,GAAA,KAAA;;;;mBAGb,WAAW,QAAQ,aAAnB,QAAA,CAAA,GAAA,CAAA;AAEM,gCAAiB,KAAA,WAAW,QAAQ,uBAAiB,QAAA,OAAA,SAAA,KAAI,YAAA;oBAC3D,kBAAkB,gBAAlB,QAAA,CAAA,GAAA,CAAA;AACM,sCAAwB,OAAO,WAAW,QAAQ,sBAAsB,aAC5E,WAAW,QAAQ,kBAAkB,eAAe,IACpD,WAAW,QAAQ;AAErB,qBAAA,CAAA,GAAM,QAAQ,0BAAqB,QAArB,0BAAqB,SAArB,wBAAyB,YAAA,6BAA6B,CAAC;;AAArE,iBAAA,KAAA;AACA,qBAAA,CAAA,IAAO,GAAA,QAAA,QAAO,KAAK,YAAY,kBAAkB,CAAC,CAAC;;AAEnD,eAAA,MAAA,KAAA,WAAW,SAAQ,qBAAe,QAAA,OAAA,SAAA,SAAA,GAAA,KAAA,IAAG,eAAe;AACpD,qBAAA,CAAA,GAAO,IAAI;;;;AAGf,qBAAA,CAAA,GAAO,IAAI;;;;;;AAIf,6BAAe;;;AAGX,0BAAY,WAAW,QAAQ,gBACnC,GAAA,YAAA,kBAAiB,YAAY,IAC7B;AAEI,sCAAwB,WAAW,QAAQ,eAC/C,GAAA,YAAA,mBACE,WACA,WAAW,QAAQ,WAAW,IAEhC;AAEF,qBAAA,CAAA,GAAO,qBAAqB;;;;;AA7CjB,YAAA,SAAM;;;;;;;;;;ACGZ,QAAM,mBAAmB,SAC9B,WACA,OAAmC;AAGnC,aAAO,IAAI,MAAiB,WAAW;QACrC,KAAK,SAAC,KAAK,KAAoB;AAC7B,cAAM,MAAM,IAAI,GAAG;AACnB,cAAK,QAAgB;AAAa,mBAAO;AACzC,cAAI,OAAO,QAAQ,YAAY;AAC7B,oBAAQ,MAAM,+HAA+H;AAG7I,mBAAO,WAAA;YAAO;UAChB,OAAO;AACL,mBAAO;UACT;QACF;QACA,KAAK,SAAoC,KAAgB,KAAQ,KAAiB;AAChF,cAAI,MAAM,KAAK,GAAG,GAAG;AACnB,oBAAQ,KAAK,uGAAwG;AACrH,mBAAO;UACT,OAAO;AACL,gBAAI,GAAG,IAAI;AACX,mBAAO;UACT;QACF;OACD;IACH;AA5Ba,YAAA,mBAAgB;AA8B7B,YAAA,UAAe,QAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACxCf,QAAA,UAAA;AACA,QAAA,cAAA;AACA,QAAA,cAAA;AACA,QAAA,mBAAA;AACA,QAAA,YAAA;AACA,QAAA,UAAA,gBAAA,eAAA;AAUA,QAAA,SAAA;AAEO,QAAM,eAAe,SAC1B,KACA,SACA,SAAuB;AADvB,UAAA,YAAA,QAAA;AAAA,kBAAmB,YAAA;MAAe;AAClC,UAAA,YAAA,QAAA;AAAA,kBAAA;MAAuB;AAEjB,UAAA,MAAgC,GAAA,QAAA,UAA8C,IAAI,GAAjF,cAAW,GAAA,CAAA,GAAE,iBAAc,GAAA,CAAA;AAC5B,UAAA,MAA8B,GAAA,QAAA,UAA0B,CAAA,CAAE,GAAzD,aAAU,GAAA,CAAA,GAAE,gBAAa,GAAA,CAAA;AAChC,UAAM,mBAAqB,GAAA,QAAA,SAAQ,WAAA;AACjC,YAAI,CAAC,QAAQ,eAAe,aAAa;AACvC,cAAI;AACF,mBAAO,KAAK,MAAM,YAAY,IAAI;UACpC,SAAS,GAAG;AACV,mBAAO,YAAA;UACT;QACF;AACA,eAAO;MACT,GAAG,CAAC,aAAa,QAAQ,WAAW,CAAC;AACrC,UAAM,gBAAe,GAAA,QAAA,QAAsB,IAAI;AAC/C,UAAM,gBAAe,GAAA,QAAA,QAA6B,IAAI;AACtD,UAAM,YAAW,GAAA,QAAA,QAAmB,WAAA;AAAM,eAAA;MAAA,CAAM;AAChD,UAAM,kBAAiB,GAAA,QAAA,QAAe,CAAC;AACvC,UAAM,mBAAkB,GAAA,QAAA,QAAe,KAAK,IAAG,CAAE;AACjD,UAAM,gBAAe,GAAA,QAAA,QAA2B,CAAA,CAAE;AAClD,UAAM,kBAAiB,GAAA,QAAA,QAA6B,IAAI;AACxD,UAAM,gBAAe,GAAA,QAAA,QAAgB,OAAO;AAC5C,mBAAa,UAAU;AAEvB,UAAM,oBACJ,aAAa,WAAW,WAAW,aAAa,OAAO,MAAM,SAC3D,WAAW,aAAa,OAAO,IAC/B,QAAQ,QAAQ,YAAY,OAC1B,YAAA,WAAW,aACX,YAAA,WAAW;AAEjB,UAAM,yBAAyB,QAAQ,cAAc,KAAK,UAAU,QAAQ,WAAW,IAAI;AAE3F,UAAM,eAA2B,GAAA,QAAA,aAAY,SAAC,SAAS,MAAW;;AAAX,YAAA,SAAA,QAAA;AAAA,iBAAA;QAAW;AAChE,YAAI,YAAA,0BAA0B,aAAa,mBAAmB,aAAa;AACzE,kBAAQ,KAAK,8CAA8C;AAC3D;QACF;AAEA,cAAIC,MAAA,aAAa,aAAO,QAAAA,QAAA,SAAA,SAAAA,IAAE,gBAAe,YAAA,WAAW,MAAM;AACxD,WAAA,GAAA,OAAA,mBAAkB,aAAa,SAAS,aAAa,QAAQ,UAAU;AACvE,uBAAa,QAAQ,KAAK,OAAO;QACnC,WAAW,MAAM;AACf,uBAAa,QAAQ,KAAK,OAAO;QACnC;MACF,GAAG,CAAA,CAAE;AAEL,UAAM,mBAAmC,GAAA,QAAA,aAAY,SAAC,SAAS,MAAW;AAAX,YAAA,SAAA,QAAA;AAAA,iBAAA;QAAW;AACxE,oBAAY,KAAK,UAAU,OAAO,GAAG,IAAI;MAC3C,GAAG,CAAC,WAAW,CAAC;AAEhB,UAAM,gBAAe,GAAA,QAAA,aAAY,WAAA;AAC/B,YAAI,aAAa,QAAQ,UAAU,QAAS,YAAA,0BAA0B,aAAa,mBAAmB,aAAc;AAClH,iBAAO,aAAa;QACtB;AAEA,YAAI,eAAe,YAAY,QAAQ,aAAa,SAAS;AAC3D,WAAA,GAAA,OAAA,mBAAkB,aAAa,SAAS,aAAa,QAAQ,UAAU;AACvE,yBAAe,WAAU,GAAA,QAAA,SAAiB,aAAa,SAAS,QAAQ;QAC1E;AAEA,eAAO,eAAe;MACxB,GAAG,CAAA,CAAE;AAEL,OAAA,GAAA,QAAA,WAAU,WAAA;AACR,YAAI,QAAQ,QAAQ,YAAY,MAAM;AACpC,cAAI;AACJ,cAAI,gBAAc;AAClB,cAAI,iBAAe;AAEnB,cAAM,UAAQ,WAAA;AAAA,mBAAA,UAAA,QAAA,QAAA,QAAA,WAAA;;;;;AACZ,oBAAAA,MAAA;AAAuB,2BAAA,CAAA,IAAM,GAAA,UAAA,QAAO,KAAK,YAAY,CAAC;;AAAtD,oBAAAA,IAAa,UAAUC,IAAA,KAAA;AAEvB,wBAAI,aAAa,YAAY,MAAM;AACjC,8BAAQ,MAAM,0DAA0D;AACxE,mCAAa,UAAU;AACvB,uBAAA,GAAA,YAAA,WAAU,WAAA;AAAM,+BAAA,cAAc,SAAA,MAAI;AAAI,iCAAA,SAAA,SAAA,CAAA,GACjC,IAAI,GAAA,EACP,SAAS,YAAA,WAAW,OAAM,CAAA;wBAFU,CAGpC;sBAHc,CAGb;AAEH,6BAAA;wBAAA;;sBAAA;oBACF;AAEM,8CAA0B,SAAC,SAAqC;AACpE,0BAAI,CAAC,eAAa;AAChB,yBAAA,GAAA,YAAA,WAAU,WAAA;AAAM,iCAAA,eAAe,OAAO;wBAAtB,CAAuB;sBACzC;oBACF;AAEM,6CAAyB,SAAC,OAAiB;AAC/C,0BAAI,CAAC,eAAa;AAChB,yBAAA,GAAA,YAAA,WAAU,WAAA;AAAM,iCAAA,cAAc,SAAA,MAAI;;AAAI,mCAAA,SAAA,SAAA,CAAA,GACjC,IAAI,GACH,aAAa,YAAOD,MAAA,CAAA,GAAMA,IAAC,aAAa,OAAO,IAAG,OAAKA,IAAG;0BAF1B,CAGpC;wBAHc,CAGb;sBACL;oBACF;AAEA,wBAAI,gBAAc;AAChB,2CAAkB,GAAA,iBAAA,oBAChB,cACA,aAAa,SACb,wBACA,cACA,yBACA,UACA,gBACA,iBACA,WAAW;oBAEf;;;;;;;;;AAGF,mBAAS,UAAU,WAAA;AACjB,gBAAI,CAAC,eAAa;AAChB,kBAAI,eAAe;AAAS,+BAAe,UAAU;AACrD,oCAAe,QAAf,sBAAe,SAAA,SAAf,kBAAe;AACf,sBAAK;YACP;UACF;AAEA,kBAAK;AACL,iBAAO,WAAA;AACL,4BAAc;AACd,6BAAe;AACf,gBAAI,eAAe;AAAS,6BAAe,UAAU;AACrD,kCAAe,QAAf,sBAAe,SAAA,SAAf,kBAAe;AACf,2BAAe,IAAI;UACrB;QACF,WAAW,QAAQ,QAAQ,YAAY,OAAO;AAC5C,yBAAe,UAAU;AACzB,wBAAc,SAAA,MAAI;;AAAI,mBAAA,SAAA,SAAA,CAAA,GACjB,IAAI,GACH,aAAa,YAAOA,MAAA,CAAA,GAAMA,IAAC,aAAa,OAAO,IAAG,YAAA,WAAW,QAAMA,IAAG;UAFtD,CAGpB;QACJ;MACF,GAAG,CAAC,KAAK,SAAS,wBAAwB,WAAW,CAAC;AAEtD,OAAA,GAAA,QAAA,WAAU,WAAA;AACR,YAAI,sBAAsB,YAAA,WAAW,MAAM;AACzC,uBAAa,QAAQ,OAAO,CAAC,EAAE,QAAQ,SAAA,SAAO;AAC5C,wBAAY,OAAO;UACrB,CAAC;QACH;MACF,GAAG,CAAC,iBAAiB,CAAC;AAEtB,aAAO;QACL;QACA;QACA;QACA;QACA,YAAY;QACZ;;IAEJ;AA9Ja,YAAA,eAAY;;;;;;;;;;;;;;;;;;;;;ACjBzB,QAAA,UAAA;AACA,QAAA,kBAAA;AACA,QAAA,cAAA;AAQA,QAAM,aAAwC;MAC5C,MAAM;MACN,SAAS;;AAGX,QAAM,gBAAgB,SAAc,OAA0C;AAC5E,UAAI,CAAC,SAAS,CAAC,MAAM,MAAM;AACzB,eAAO;MACT;AAEA,UAAM,QAAQ,MAAM,KAAK,MAAM,OAAO;AAEtC,UAAI,CAAC,OAAO;AACV,eAAO;MACT;AAEA,UAAM,OAAO,KAAK,MAAM,KAAK;AAE7B,UAAI,CAAC,MAAM,QAAQ,IAAI,KAAK,CAAC,KAAK,CAAC,GAAG;AACpC,eAAO;MACT;AAEA,aAAO;QACL,MAAM,KAAK,CAAC;QACZ,SAAS,KAAK,CAAC;;IAEnB;AAEO,QAAM,cAAc,SACzB,KACA,SACA,SAAuB;AADvB,UAAA,YAAA,QAAA;AAAA,kBAAmB,YAAA;MAAe;AAClC,UAAA,YAAA,QAAA;AAAA,kBAAA;MAAuB;AAEvB,UAAM,uBAAsB,GAAA,QAAA,SAAQ,WAAA;AAAM,eAAA,SAAA,SAAA,CAAA,GACrC,OAAO,GAAA,EACV,cAAc,KAAI,CAAA;MAFsB,GAGtC,CAAA,CAAE;AAEA,UAAA,MAMF,GAAA,gBAAA,cAEF,KACA,qBACA,OAAO,GATP,cAAW,GAAA,aACX,kBAAe,GAAA,iBACf,cAAW,GAAA,aACX,aAAU,GAAA,YACV,eAAY,GAAA;AAQd,UAAM,uBAAsB,GAAA,QAAA,SAAQ,WAAA;AAClC,eAAA,cAAiB,WAAW;MAA5B,GAA+B,CAAC,WAAW,CAAC;AAE9C,aAAO;QACL;QACA;QACA,aAAa;QACb,iBAAiB;QACjB;QACA;;IAEJ;AAlCa,YAAA,cAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACtCxB,QAAA,UAAA;AACA,QAAA,kBAAA;AACA,QAAA,cAAA;AAGO,QAAM,iBAAiB,SAC5B,KACA,IACA,SAAuB;AADvB,UAAA,OAAA,QAAA;AAAA,aAA8D,YAAA;MAA4B;AAAxF,UAAA,kBAAe,GAAA,iBAAE,SAAM,GAAA,QAAK,UAAO,OAAA,IAArC,CAAA,mBAAA,QAAA,CAAuC;AACvC,UAAA,YAAA,QAAA;AAAA,kBAAA;MAAuB;AAEvB,UAAM,yBAAsB,SAAA,SAAA,CAAA,GACrB,OAAO,GAAA,EACV,oBAAoB;QAClB;QACD,CAAA;AAEL,UAAM,aAAY,GAAA,QAAA,QAAiC,YAAA,oBAAoB;AACvE,UAAI,QAAQ;AACV,kBAAU,UAAU;MACtB;AAEM,UAAA,MAIF,GAAA,gBAAA,cACF,KACA,wBACA,OAAO,GANP,cAAW,GAAA,aACX,aAAU,GAAA,YACV,eAAY,GAAA;AAOd,OAAA,GAAA,QAAA,WAAU,WAAA;AACR,YAAI,gBAAW,QAAX,gBAAW,SAAA,SAAX,YAAa,MAAM;AACnB,iBAAO,QAAQ,UAAU,OAAO,EAAE,QAAQ,SAACE,KAAe;gBAAd,OAAIA,IAAA,CAAA,GAAE,UAAOA,IAAA,CAAA;AACrD,gBAAI,SAAS,YAAY,MAAM;AAC3B,sBAAQ,WAAW;YACvB;UACJ,CAAC;QACL;MACF,GAAG,CAAC,WAAW,CAAC;AAEhB,aAAO;QACL,WAAW;QACX;QACA,gBAAgB;;IAEpB;AAzCa,YAAA,iBAAc;;;;;;;;;ACL3B,QAAA,kBAAA;AAAS,WAAA,eAAA,SAAA,WAAA,EAAA,YAAA,MAAA,KAAA,WAAA;AAAA,aAAA,gBAAA;IAAY,EAAA,CAAA;AAIrB,QAAA,kBAAA;AAAS,WAAA,eAAA,SAAA,eAAA,EAAA,YAAA,MAAA,KAAA,WAAA;AAAA,aAAA,gBAAA;IAAW,EAAA,CAAA;AAEpB,QAAA,cAAA;AAAS,WAAA,eAAA,SAAA,cAAA,EAAA,YAAA,MAAA,KAAA,WAAA;AAAA,aAAA,YAAA;IAAU,EAAA,CAAA;AAEnB,QAAA,qBAAA;AAAS,WAAA,eAAA,SAAA,kBAAA,EAAA,YAAA,MAAA,KAAA,WAAA;AAAA,aAAA,mBAAA;IAAc,EAAA,CAAA;AAEvB,QAAA,SAAA;AAAS,WAAA,eAAA,SAAA,oBAAA,EAAA,YAAA,MAAA,KAAA,WAAA;AAAA,aAAA,OAAA;IAAgB,EAAA,CAAA;;;", "names": ["ReadyState", "_a", "_b", "_a"]}